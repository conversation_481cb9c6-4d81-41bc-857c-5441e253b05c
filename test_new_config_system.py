#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的 YouTube 自动化配置系统
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config_manager import ConfigManager


def test_new_config_system():
    """测试新的配置系统"""
    print("🧪 测试新的 YouTube 自动化配置系统")
    print("=" * 60)
    
    config_manager = ConfigManager()
    
    # 1. 测试配置文件加载
    print("\n1. 测试配置文件加载:")
    try:
        config = config_manager.load_config()
        if "youtube_automation" in config:
            print("✅ 检测到新配置格式")
            print(f"   - 浏览器数据同步时间: {config['browser_data']['last_sync']}")
            print(f"   - 总浏览器数: {config['browser_data']['total_browsers']}")
        else:
            print("⚠️  使用旧配置格式")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    # 2. 测试获取所有浏览器
    print("\n2. 测试获取所有浏览器:")
    try:
        all_browsers = config_manager.get_browsers()
        print(f"✅ 获取到 {len(all_browsers)} 个浏览器配置")
        
        # 按分组统计
        groups = {}
        for browser in all_browsers:
            group_name = browser.get("group_name", "未知分组")
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(browser)
        
        print("   分组统计:")
        for group_name, browsers in groups.items():
            print(f"     - {group_name}: {len(browsers)} 个浏览器")
            
    except Exception as e:
        print(f"❌ 获取浏览器失败: {e}")
        return False
    
    # 3. 测试获取 YouTube 自动化浏览器
    print("\n3. 测试获取 YouTube 自动化浏览器:")
    try:
        youtube_browsers = config_manager.get_youtube_enabled_browsers()
        print(f"✅ 获取到 {len(youtube_browsers)} 个参与 YouTube 自动化的浏览器")
        
        for browser in youtube_browsers:
            name = browser.get("name", "未命名")
            group_name = browser.get("group_name", "未知分组")
            print(f"     - {name} (分组: {group_name})")
            
    except Exception as e:
        print(f"❌ 获取 YouTube 浏览器失败: {e}")
        return False
    
    # 4. 测试根据 ID 获取浏览器数据
    print("\n4. 测试根据 ID 获取浏览器数据:")
    try:
        # 测试获取第一个浏览器的详细数据
        if youtube_browsers:
            test_id = youtube_browsers[0]["id"]
            browser_data = config_manager.get_browser_data_by_id(test_id)
            
            if browser_data:
                print(f"✅ 成功获取浏览器 {test_id} 的详细数据:")
                print(f"     - 名称: {browser_data.get('name', '未知')}")
                print(f"     - 分组: {browser_data.get('group_name', '未知')}")
                print(f"     - 代理类型: {browser_data.get('proxy_type', '未知')}")
                print(f"     - 备注: {browser_data.get('note', '无')}")
            else:
                print(f"❌ 未找到浏览器 {test_id} 的数据")
        else:
            print("⚠️  没有可测试的浏览器")
            
    except Exception as e:
        print(f"❌ 获取浏览器数据失败: {e}")
        return False
    
    # 5. 测试配置统计
    print("\n5. 配置统计:")
    try:
        total_browsers = len(all_browsers)
        youtube_browsers_count = len(youtube_browsers)
        non_youtube_count = total_browsers - youtube_browsers_count
        
        print(f"   📊 总浏览器数: {total_browsers}")
        print(f"   ✅ 参与 YouTube 自动化: {youtube_browsers_count}")
        print(f"   ❌ 不参与自动化: {non_youtube_count}")
        
        if "youtube_automation" in config:
            group_settings = config["youtube_automation"]["group_settings"]
            print(f"   📂 分组设置:")
            for group_name, settings in group_settings.items():
                project_type = settings.get("project_type", "unknown")
                auto_include = settings.get("auto_include", False)
                status = "自动包含" if auto_include else "手动设置"
                print(f"     - {group_name}: {project_type} 项目 ({status})")
        
    except Exception as e:
        print(f"❌ 统计失败: {e}")
        return False
    
    print("\n🎉 所有测试通过!")
    return True


def test_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性")
    print("-" * 40)
    
    config_manager = ConfigManager()
    
    try:
        # 测试旧方法是否仍然工作
        browsers = config_manager.get_browsers()
        settings = config_manager.get_settings()
        
        print(f"✅ 旧方法兼容性测试通过")
        print(f"   - get_browsers() 返回 {len(browsers)} 个浏览器")
        print(f"   - get_settings() 返回 {len(settings)} 个设置项")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 YouTube 自动化配置系统测试")
    print("=" * 60)
    
    # 测试新配置系统
    if not test_new_config_system():
        print("\n❌ 新配置系统测试失败")
        return
    
    # 测试向后兼容性
    if not test_compatibility():
        print("\n❌ 向后兼容性测试失败")
        return
    
    print("\n🎉 所有测试完成!")
    print("\n💡 使用说明:")
    print("   1. 运行 'python browser_config_sync.py' 配置 YouTube 自动化")
    print("   2. 运行 'python sequential_automation.py' 开始自动化")
    print("   3. 只有设置为参与的浏览器才会执行 YouTube 自动化")


if __name__ == "__main__":
    main()
