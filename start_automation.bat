@echo off
chcp 65001 >nul
title YouTube Shorts 自动化机器人

echo.
echo ========================================
echo    YouTube Shorts 自动化机器人
echo ========================================
echo.
echo 正在启动自动化任务...
echo 执行模式: 随机顺序执行
echo 无需确认，自动开始
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Python
    echo 请确保已安装 Python 并添加到 PATH 环境变量
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "sequential_automation.py" (
    echo ❌ 错误: 未找到 sequential_automation.py 文件
    echo 请确保在正确的目录中运行此批处理文件
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "config\config.json" (
    echo ❌ 错误: 未找到配置文件 config\config.json
    echo 请确保配置文件存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

REM 运行自动化脚本
echo 🚀 启动自动化任务...
echo.
python sequential_automation.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ❌ 自动化任务执行失败
    echo 请检查错误信息并重试
) else (
    echo.
    echo ✅ 自动化任务执行完成
)

echo.
echo 按任意键退出...
pause >nul
