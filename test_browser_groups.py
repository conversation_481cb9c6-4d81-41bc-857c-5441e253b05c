#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器分组功能
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config_manager import Config<PERSON>ana<PERSON>


def test_browser_groups():
    """测试浏览器分组功能"""
    print("🧪 测试浏览器分组功能")
    print("=" * 50)
    
    config_manager = ConfigManager()
    
    # 1. 测试获取所有分组
    print("\n1. 获取所有分组:")
    groups = config_manager.get_all_groups()
    print(f"   现有分组: {groups}")
    
    # 2. 测试按分组获取浏览器
    print("\n2. 按分组获取浏览器:")
    for group in groups:
        browsers = config_manager.get_browsers_by_group(group)
        print(f"   分组 '{group}': {len(browsers)} 个浏览器")
        for browser in browsers:
            print(f"     - {browser['name']} (ID: {browser['id']})")
    
    # 3. 测试添加带分组的浏览器
    print("\n3. 测试添加带分组的浏览器:")
    test_id = "test_999"
    test_name = "测试浏览器"
    test_group = "测试分组"
    
    # 先删除可能存在的测试浏览器
    config_manager.remove_browser(test_id)
    
    # 添加测试浏览器
    if config_manager.add_browser_with_group(test_id, test_name, test_group, True):
        print(f"   ✅ 成功添加浏览器: {test_name} -> {test_group}")
        
        # 验证添加结果
        test_browsers = config_manager.get_browsers_by_group(test_group)
        print(f"   验证: 分组 '{test_group}' 现在有 {len(test_browsers)} 个浏览器")
    else:
        print(f"   ❌ 添加浏览器失败")
    
    # 4. 测试更新浏览器分组
    print("\n4. 测试更新浏览器分组:")
    new_group = "新测试分组"
    if config_manager.update_browser_group(test_id, new_group):
        print(f"   ✅ 成功更新浏览器分组: {test_id} -> {new_group}")
        
        # 验证更新结果
        new_browsers = config_manager.get_browsers_by_group(new_group)
        old_browsers = config_manager.get_browsers_by_group(test_group)
        print(f"   验证: 分组 '{new_group}' 现在有 {len(new_browsers)} 个浏览器")
        print(f"   验证: 分组 '{test_group}' 现在有 {len(old_browsers)} 个浏览器")
    else:
        print(f"   ❌ 更新浏览器分组失败")
    
    # 5. 清理测试数据
    print("\n5. 清理测试数据:")
    if config_manager.remove_browser(test_id):
        print(f"   ✅ 成功删除测试浏览器: {test_id}")
    else:
        print(f"   ❌ 删除测试浏览器失败")
    
    # 6. 最终状态
    print("\n6. 最终分组状态:")
    final_groups = config_manager.get_all_groups()
    for group in final_groups:
        browsers = config_manager.get_browsers_by_group(group)
        print(f"   分组 '{group}': {len(browsers)} 个浏览器")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    test_browser_groups()
