@echo off
chcp 65001 >nul
title YouTube 自动化 - 快速启动

echo.
echo 🚀 YouTube Shorts 自动化机器人
echo ================================
echo 模式: 随机顺序 + 自动执行
echo.

REM 快速检查并启动
if not exist "sequential_automation.py" (
    echo ❌ 找不到程序文件
    pause
    exit /b 1
)

if not exist "config\config.json" (
    echo ❌ 找不到配置文件
    pause
    exit /b 1
)

echo ✅ 正在启动...
echo.

python sequential_automation.py

echo.
echo 任务完成，按任意键退出...
pause >nul
