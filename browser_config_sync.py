#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器配置同步脚本
从 ixBrowser API 读取浏览器列表并同步到配置文件，支持分组功能
"""

import sys
import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.browser_api import ixBrowserAPI
from utils.config_manager import ConfigManager
from utils.logger_setup import get_logger

# 设置日志
logger = get_logger("browser_config_sync")


class BrowserConfigSync:
    """浏览器配置同步器"""
    
    def __init__(self):
        """初始化同步器"""
        self.browser_api = ixBrowserAPI()
        self.config_manager = ConfigManager()
        
    def check_api_connection(self) -> bool:
        """
        检查 API 连接
        
        Returns:
            bool: API 是否可用
        """
        print("正在检查 ixBrowser API 连接...")
        if self.browser_api.check_api_server():
            print("✅ ixBrowser API 连接正常")
            return True
        else:
            print("❌ ixBrowser API 连接失败")
            print("请确保 ixBrowser 软件正在运行")
            return False
    
    def fetch_browser_list(self) -> Optional[List[Dict[str, Any]]]:
        """
        从 API 获取浏览器列表
        
        Returns:
            Optional[List[Dict[str, Any]]]: 浏览器列表，失败时返回 None
        """
        print("正在从 ixBrowser API 获取浏览器列表...")
        
        try:
            browser_data = self.browser_api.get_browser_list()
            if not browser_data:
                print("❌ 获取浏览器列表失败")
                return None
            
            # 解析浏览器列表
            browsers = []
            if isinstance(browser_data, dict):
                # 新 API 格式: data.data
                if 'data' in browser_data and isinstance(browser_data['data'], dict):
                    if 'data' in browser_data['data'] and isinstance(browser_data['data']['data'], list):
                        browsers = browser_data['data']['data']
                # 备用格式
                elif 'data' in browser_data and isinstance(browser_data['data'], list):
                    browsers = browser_data['data']
                elif 'list' in browser_data and isinstance(browser_data['list'], list):
                    browsers = browser_data['list']
            elif isinstance(browser_data, list):
                browsers = browser_data
            
            if not browsers:
                print("❌ 没有找到浏览器配置")
                return None
            
            print(f"✅ 成功获取 {len(browsers)} 个浏览器配置")
            return browsers
            
        except Exception as e:
            logger.error(f"获取浏览器列表时发生错误: {e}")
            print(f"❌ 获取浏览器列表时发生错误: {e}")
            return None
    
    def display_browser_list(self, browsers: List[Dict[str, Any]]) -> None:
        """
        显示浏览器列表
        
        Args:
            browsers: 浏览器列表
        """
        print("\n📋 从 ixBrowser API 获取的浏览器列表:")
        print("-" * 60)
        for i, browser in enumerate(browsers, 1):
            profile_id = browser.get('profile_id', browser.get('id', '未知'))
            name = browser.get('name', browser.get('profile_name', '未命名'))
            status = browser.get('status', '未知状态')
            group_id = browser.get('group_id', 0)
            print(f"{i:2d}. ID: {str(profile_id):4s} | 名称: {name:20s} | 状态: {status:8s} | 分组ID: {group_id}")
    
    def get_user_group_selection(self) -> str:
        """
        获取用户选择的分组名称
        
        Returns:
            str: 分组名称
        """
        print("\n🏷️  分组设置:")
        print("1. 使用默认分组")
        print("2. 输入自定义分组名称")
        print("3. 查看现有分组")
        
        while True:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == "1":
                return "默认分组"
            elif choice == "2":
                group_name = input("请输入分组名称: ").strip()
                if group_name:
                    return group_name
                else:
                    print("❌ 分组名称不能为空")
            elif choice == "3":
                existing_groups = self.config_manager.get_all_groups()
                if existing_groups:
                    print("\n现有分组:")
                    for i, group in enumerate(existing_groups, 1):
                        print(f"  {i}. {group}")
                    
                    group_choice = input(f"\n选择现有分组 (1-{len(existing_groups)}) 或按回车返回: ").strip()
                    if group_choice.isdigit():
                        idx = int(group_choice) - 1
                        if 0 <= idx < len(existing_groups):
                            return existing_groups[idx]
                else:
                    print("暂无现有分组")
            else:
                print("❌ 无效选择，请重新输入")
    
    def sync_browsers_to_config(self, browsers: List[Dict[str, Any]], group_name: str) -> bool:
        """
        同步浏览器到配置文件
        
        Args:
            browsers: 浏览器列表
            group_name: 分组名称
            
        Returns:
            bool: 是否同步成功
        """
        print(f"\n🔄 正在同步浏览器到配置文件 (分组: {group_name})...")
        
        try:
            # 获取现有配置
            existing_browsers = self.config_manager.get_browsers()
            existing_ids = {b.get("id") for b in existing_browsers}
            
            added_count = 0
            updated_count = 0
            
            for browser in browsers:
                profile_id = str(browser.get('profile_id', browser.get('id', '')))
                name = browser.get('name', browser.get('profile_name', f'浏览器_{profile_id}'))
                
                if not profile_id:
                    logger.warning(f"跳过无效的浏览器配置: {browser}")
                    continue
                
                if profile_id in existing_ids:
                    # 更新现有浏览器的分组
                    if self.config_manager.update_browser_group(profile_id, group_name):
                        updated_count += 1
                        logger.info(f"更新浏览器分组: {name} ({profile_id}) -> {group_name}")
                else:
                    # 添加新浏览器
                    if self.config_manager.add_browser_with_group(profile_id, name, group_name, True):
                        added_count += 1
                        logger.info(f"添加新浏览器: {name} ({profile_id}) -> {group_name}")
            
            print(f"✅ 同步完成!")
            print(f"   - 新增浏览器: {added_count} 个")
            print(f"   - 更新分组: {updated_count} 个")
            
            return True
            
        except Exception as e:
            logger.error(f"同步浏览器配置时发生错误: {e}")
            print(f"❌ 同步失败: {e}")
            return False
    
    def run(self) -> None:
        """运行同步流程"""
        print("🚀 ixBrowser 配置同步工具")
        print("=" * 50)
        
        # 检查 API 连接
        if not self.check_api_connection():
            return
        
        # 获取浏览器列表
        browsers = self.fetch_browser_list()
        if not browsers:
            return
        
        # 显示浏览器列表
        self.display_browser_list(browsers)
        
        # 获取分组选择
        group_name = self.get_user_group_selection()
        
        # 确认同步
        print(f"\n📝 即将同步 {len(browsers)} 个浏览器到分组 '{group_name}'")
        confirm = input("确认继续? (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            if self.sync_browsers_to_config(browsers, group_name):
                print("\n🎉 配置同步完成!")
                
                # 显示最终配置
                print(f"\n📊 当前配置统计:")
                all_groups = self.config_manager.get_all_groups()
                for group in all_groups:
                    count = len(self.config_manager.get_browsers_by_group(group))
                    print(f"   - {group}: {count} 个浏览器")
            else:
                print("\n❌ 配置同步失败!")
        else:
            print("\n❌ 用户取消同步")


def main():
    """主函数"""
    try:
        sync_tool = BrowserConfigSync()
        sync_tool.run()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        logger.error(f"程序运行时发生未知错误: {e}")
        print(f"\n❌ 程序运行时发生未知错误: {e}")


if __name__ == "__main__":
    main()
