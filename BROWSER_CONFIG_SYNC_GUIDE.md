# YouTube 自动化浏览器配置工具使用指南

## 概述

`browser_config_sync.py` 是一个全新的 YouTube 自动化浏览器配置管理工具，直接从 ixBrowser API 获取完整浏览器数据，支持多项目分组管理和 YouTube 自动化参与设置。

## 功能特性

- ✅ 直接从 ixBrowser API 获取完整浏览器数据
- ✅ 支持多项目分组管理（YouTube、Docker 等）
- ✅ YouTube 自动化参与设置
- ✅ 按分组批量配置或单独设置
- ✅ 实时显示浏览器详细信息（代理、最后打开时间等）
- ✅ 新旧配置格式兼容
- ✅ 友好的交互式界面
- ✅ 完整的错误处理和日志记录

## 全新配置文件结构

### 新的配置文件格式

配置文件现在分为三个主要部分：

```json
{
    "browser_data": {
        "last_sync": 1752630507,
        "total_browsers": 12,
        "browsers": {
            "1": {
                "profile_id": 1,
                "name": "YouTube_猫咪",
                "note": "",
                "group_id": 202389,
                "group_name": "YouTube",
                "last_open_time": 1752588480,
                "proxy_type": "socks5",
                "color": "#FFD700",
                "tag_name": ""
            }
        }
    },
    "youtube_automation": {
        "enabled_browsers": {
            "1": {
                "participate": true,
                "note": "自动包含 - YouTube"
            }
        },
        "group_settings": {
            "YouTube": {
                "auto_include": true,
                "project_type": "youtube"
            },
            "docker": {
                "auto_include": false,
                "project_type": "other"
            }
        }
    },
    "settings": {
        "min_watch_time": 3,
        "max_watch_time": 8,
        "like_probability": 0.3,
        "subscribe_probability": 0.1,
        "scroll_count": 50
    }
}
```

### 配置结构说明

- **browser_data**: 从 ixBrowser API 获取的完整浏览器数据
- **youtube_automation**: YouTube 自动化专用配置
  - `enabled_browsers`: 每个浏览器的参与设置
  - `group_settings`: 分组级别的项目类型设置
- **settings**: 自动化运行参数

## 使用方法

### 1. 确保 ixBrowser 运行

在使用同步工具之前，请确保：
- ixBrowser 软件正在运行
- API 服务可用 (默认端口: 53200)

### 2. 运行同步工具

```bash
python browser_config_sync.py
```

### 3. 操作流程

1. **API 连接检查**: 工具会自动检查 ixBrowser API 连接状态
2. **获取完整浏览器数据**: 从 API 获取所有浏览器的详细信息
3. **显示浏览器列表**: 按分组展示浏览器信息，包括：
   - 浏览器名称和 ID
   - 分组信息
   - 备注信息
   - 代理类型
   - 最后打开时间
4. **YouTube 自动化设置**:
   - 按分组设置项目类型
   - 选择是否参与 YouTube 自动化
   - 支持批量设置或单独配置
5. **配置摘要**: 显示最终配置统计
6. **确认保存**: 确认后保存新的配置结构
7. **完成**: 显示配置结果和使用提示

### 4. 项目分组管理

#### 分组级别设置
工具会自动识别 ixBrowser 中的分组，并提供三种设置选项：

- **y (是)**: 整个分组参与 YouTube 自动化
- **n (否)**: 整个分组不参与 YouTube 自动化
- **s (单独设置)**: 对分组内每个浏览器单独配置

#### 项目类型识别
- **youtube**: YouTube 自动化项目
- **other**: 其他项目（如 Docker、测试等）
- **mixed**: 混合项目（需要单独设置每个浏览器）

#### 实际使用场景
- **YouTube 分组**: 设置为 youtube 项目，自动包含所有浏览器
- **Docker 分组**: 设置为 other 项目，排除所有浏览器
- **测试分组**: 设置为 mixed 项目，单独选择需要的浏览器

## 新增的配置管理功能

### ConfigManager 新方法

```python
# 添加带分组的浏览器
config_manager.add_browser_with_group("1", "YouTube_猫咪", "YouTube 频道", True)

# 获取指定分组的浏览器
browsers = config_manager.get_browsers_by_group("YouTube 频道")

# 获取所有分组
groups = config_manager.get_all_groups()

# 更新浏览器分组
config_manager.update_browser_group("1", "新分组")
```

## 示例使用场景

### 场景 1: 多项目环境初次配置
**背景**: ixBrowser 中有 YouTube 和 Docker 两个分组

1. 运行配置工具: `python browser_config_sync.py`
2. 工具显示所有浏览器按分组分类
3. 对于 "YouTube" 分组，选择 `y` (参与自动化)
4. 对于 "docker" 分组，选择 `n` (不参与自动化)
5. 确认保存配置

**结果**: 只有 YouTube 分组的浏览器会参与自动化

### 场景 2: 混合分组精细控制
**背景**: 某个分组中既有生产环境又有测试环境的浏览器

1. 运行配置工具
2. 对于混合分组，选择 `s` (单独设置)
3. 逐个选择哪些浏览器参与 YouTube 自动化
4. 保存配置

**结果**: 实现精细化的浏览器选择控制

### 场景 3: 配置更新
**背景**: ixBrowser 中新增了浏览器或修改了分组

1. 重新运行配置工具
2. 工具会获取最新的浏览器数据
3. 重新配置 YouTube 自动化参与设置
4. 保存更新后的配置

**结果**: 配置始终与 ixBrowser 中的实际数据保持同步

## 日志和错误处理

- 所有操作都会记录到日志文件
- 详细的错误信息和处理建议
- 网络连接问题的自动检测
- 配置文件格式验证

## 注意事项

1. **配置格式变更**: 新版本使用全新的配置格式，与旧版本不兼容
2. **API 依赖**: 工具完全依赖 ixBrowser API，确保软件正在运行
3. **实时同步**: 每次运行都会获取最新的浏览器数据
4. **项目隔离**: 不同项目的浏览器可以完全隔离，避免误操作
5. **权限要求**: 确保有配置文件的读写权限

## 故障排除

### 常见问题

1. **API 连接失败**
   - 检查 ixBrowser 是否正在运行
   - 检查 API 端口是否正确 (默认 53200)
   - 检查防火墙设置

2. **配置文件写入失败**
   - 检查文件权限
   - 检查磁盘空间
   - 检查配置文件格式

3. **浏览器列表为空**
   - 检查 ixBrowser 中是否有配置的浏览器
   - 检查 API 响应格式
   - 确认 API 权限设置

4. **配置保存失败**
   - 检查磁盘空间
   - 确认文件权限
   - 检查配置文件路径

### 获取帮助

如果遇到问题，请检查：
1. 控制台输出的错误信息
2. 日志文件中的详细信息
3. ixBrowser 软件的状态和版本
4. API 端口和服务状态

## 兼容性和迁移

### 新旧版本兼容性
- ✅ 自动检测配置格式
- ✅ 旧格式配置仍可正常使用
- ✅ 新功能向后兼容

### 迁移建议
1. **首次使用**: 直接运行新配置工具
2. **现有用户**: 备份旧配置后运行新工具
3. **测试验证**: 使用 `python test_new_config_system.py` 验证配置

### 配置文件位置
- 配置文件: `config/config.json`
- 备份建议: 首次使用前手动备份
- 恢复方法: 删除新配置文件，恢复备份即可回到旧版本
