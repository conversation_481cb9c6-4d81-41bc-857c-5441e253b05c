# 浏览器配置同步工具使用指南

## 概述

`browser_config_sync.py` 是一个专门用于从 ixBrowser API 读取浏览器列表并同步到配置文件的工具，支持分组功能。

## 功能特性

- ✅ 从 ixBrowser API 自动获取浏览器列表
- ✅ 支持浏览器分组管理
- ✅ 自动同步到本地配置文件
- ✅ 支持新增和更新现有浏览器配置
- ✅ 友好的交互式界面
- ✅ 完整的错误处理和日志记录

## 配置文件格式更新

### 新的配置文件格式

现在每个浏览器配置都包含分组信息：

```json
{
    "browsers": [
        {
            "id": "1",
            "name": "YouTube_猫咪",
            "enabled": true,
            "group_name": "默认分组"
        },
        {
            "id": "2",
            "name": "乡村1",
            "enabled": true,
            "group_name": "乡村系列"
        }
    ]
}
```

### 新增字段说明

- `group_name`: 浏览器所属的分组名称，默认为 "默认分组"

## 使用方法

### 1. 确保 ixBrowser 运行

在使用同步工具之前，请确保：
- ixBrowser 软件正在运行
- API 服务可用 (默认端口: 53200)

### 2. 运行同步工具

```bash
python browser_config_sync.py
```

### 3. 操作流程

1. **API 连接检查**: 工具会自动检查 ixBrowser API 连接状态
2. **获取浏览器列表**: 从 API 获取所有浏览器配置
3. **显示浏览器列表**: 展示获取到的浏览器信息
4. **选择分组**: 
   - 选项 1: 使用默认分组
   - 选项 2: 输入自定义分组名称
   - 选项 3: 查看并选择现有分组
5. **确认同步**: 确认后开始同步配置
6. **完成**: 显示同步结果和统计信息

### 4. 分组管理

#### 创建新分组
选择 "输入自定义分组名称" 并输入新的分组名称，如：
- "YouTube 频道"
- "测试账号"
- "乡村系列"
- "故事系列"

#### 使用现有分组
选择 "查看现有分组" 可以看到所有已存在的分组，并选择其中一个。

## 新增的配置管理功能

### ConfigManager 新方法

```python
# 添加带分组的浏览器
config_manager.add_browser_with_group("1", "YouTube_猫咪", "YouTube 频道", True)

# 获取指定分组的浏览器
browsers = config_manager.get_browsers_by_group("YouTube 频道")

# 获取所有分组
groups = config_manager.get_all_groups()

# 更新浏览器分组
config_manager.update_browser_group("1", "新分组")
```

## 示例使用场景

### 场景 1: 初次设置
1. 运行同步工具
2. 选择 "使用默认分组"
3. 所有浏览器都会被添加到 "默认分组"

### 场景 2: 按用途分组
1. 运行同步工具
2. 选择 "输入自定义分组名称"
3. 输入 "YouTube 频道"
4. 所有浏览器都会被分配到 "YouTube 频道" 分组

### 场景 3: 更新现有配置
1. 在 ixBrowser 中添加新的浏览器配置
2. 运行同步工具
3. 选择合适的分组
4. 新浏览器会被添加，现有浏览器的分组会被更新

## 日志和错误处理

- 所有操作都会记录到日志文件
- 详细的错误信息和处理建议
- 网络连接问题的自动检测
- 配置文件格式验证

## 注意事项

1. **备份配置**: 首次使用前建议备份现有配置文件
2. **API 可用性**: 确保 ixBrowser 软件正在运行
3. **权限问题**: 确保有配置文件的写入权限
4. **分组命名**: 分组名称不能为空，建议使用有意义的名称

## 故障排除

### 常见问题

1. **API 连接失败**
   - 检查 ixBrowser 是否正在运行
   - 检查 API 端口是否正确 (默认 53200)
   - 检查防火墙设置

2. **配置文件写入失败**
   - 检查文件权限
   - 检查磁盘空间
   - 检查配置文件格式

3. **浏览器列表为空**
   - 检查 ixBrowser 中是否有配置的浏览器
   - 检查 API 响应格式

### 获取帮助

如果遇到问题，请检查：
1. 控制台输出的错误信息
2. 日志文件中的详细信息
3. ixBrowser 软件的状态

## 兼容性

- 向后兼容现有配置文件
- 自动为旧配置添加默认分组
- 不影响现有功能的正常使用
