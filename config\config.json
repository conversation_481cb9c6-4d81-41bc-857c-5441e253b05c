{"browser_data": {"last_sync": 1752630507, "total_browsers": 12, "browsers": {"12": {"profile_id": 12, "name": "gs_15219196694", "note": "gs_15219196694", "group_id": 204423, "group_name": "docker", "last_open_time": 1752626543, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "11": {"profile_id": 11, "name": "gs_17786956691", "note": "gs_17786956691", "group_id": 204423, "group_name": "docker", "last_open_time": 1752592884, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "10": {"profile_id": 10, "name": "gs_17357969072", "note": "gs_17357969072", "group_id": 204423, "group_name": "docker", "last_open_time": 1752592369, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "9": {"profile_id": 9, "name": "bj_18814386012", "note": "bj_18814386012", "group_id": 204423, "group_name": "docker", "last_open_time": 1752591381, "proxy_type": "socks5", "color": "#C71585", "tag_name": ""}, "8": {"profile_id": 8, "name": "bj_13138092867", "note": "bj_13138092867", "group_id": 204423, "group_name": "docker", "last_open_time": 1752590934, "proxy_type": "socks5", "color": "#C71585", "tag_name": ""}, "7": {"profile_id": 7, "name": "长视频1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752552554, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "6": {"profile_id": 6, "name": "故事1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752555472, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "5": {"profile_id": 5, "name": "乡村4", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752553639, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "4": {"profile_id": 4, "name": "乡村3", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752554742, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "3": {"profile_id": 3, "name": "乡村2", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752554344, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "2": {"profile_id": 2, "name": "乡村1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752552008, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "1": {"profile_id": 1, "name": "YouTube_猫咪", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752553087, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}}}, "youtube_automation": {"enabled_browsers": {"12": {"participate": false, "note": "分组排除 - docker"}, "11": {"participate": false, "note": "分组排除 - docker"}, "10": {"participate": false, "note": "分组排除 - docker"}, "9": {"participate": false, "note": "分组排除 - docker"}, "8": {"participate": false, "note": "分组排除 - docker"}, "7": {"participate": true, "note": "自动包含 - YouTube"}, "6": {"participate": true, "note": "自动包含 - YouTube"}, "5": {"participate": true, "note": "自动包含 - YouTube"}, "4": {"participate": true, "note": "自动包含 - YouTube"}, "3": {"participate": true, "note": "自动包含 - YouTube"}, "2": {"participate": true, "note": "自动包含 - YouTube"}, "1": {"participate": true, "note": "自动包含 - YouTube"}}, "group_settings": {"docker": {"auto_include": false, "project_type": "other"}, "YouTube": {"auto_include": true, "project_type": "youtube"}}}, "settings": {"min_watch_time": 3, "max_watch_time": 8, "like_probability": 0.3, "subscribe_probability": 0.1, "scroll_count": 50}, "api": {"base_url": "http://127.0.0.1:53200", "timeout": 30}}