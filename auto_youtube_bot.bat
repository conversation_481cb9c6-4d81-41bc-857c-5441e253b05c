@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 设置窗口标题和颜色
title YouTube Shorts 自动化机器人 - 随机顺序执行
color 0A

REM 获取当前时间
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██        YouTube Shorts 自动化机器人 v2.0               ██
echo ██                                                        ██
echo ██        执行模式: 随机顺序 + 自动运行                   ██
echo ██        启动时间: %timestamp%                    ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

REM 创建日志目录
if not exist "logs" mkdir logs

REM 设置日志文件
set "logfile=logs\automation_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log"

echo 📝 日志文件: %logfile%
echo.

REM 记录启动信息到日志
echo [%timestamp%] YouTube Shorts 自动化机器人启动 >> "%logfile%"
echo [%timestamp%] 执行模式: 随机顺序自动执行 >> "%logfile%"

REM 环境检查
echo 🔍 正在进行环境检查...
echo.

REM 检查 Python
echo 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到 PATH
    echo [%timestamp%] 错误: Python 环境检查失败 >> "%logfile%"
    goto :error_exit
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set "python_version=%%i"
echo ✅ %python_version%
echo [%timestamp%] Python 环境检查通过: %python_version% >> "%logfile%"

REM 检查必要文件
echo.
echo 检查必要文件...

if not exist "sequential_automation.py" (
    echo ❌ 缺少主程序文件: sequential_automation.py
    echo [%timestamp%] 错误: 缺少 sequential_automation.py >> "%logfile%"
    goto :error_exit
)
echo ✅ sequential_automation.py

if not exist "config\config.json" (
    echo ❌ 缺少配置文件: config\config.json
    echo [%timestamp%] 错误: 缺少配置文件 >> "%logfile%"
    goto :error_exit
)
echo ✅ config\config.json

if not exist "src\browser_api.py" (
    echo ❌ 缺少 API 文件: src\browser_api.py
    echo [%timestamp%] 错误: 缺少 browser_api.py >> "%logfile%"
    goto :error_exit
)
echo ✅ src\browser_api.py

if not exist "src\youtube_bot.py" (
    echo ❌ 缺少机器人文件: src\youtube_bot.py
    echo [%timestamp%] 错误: 缺少 youtube_bot.py >> "%logfile%"
    goto :error_exit
)
echo ✅ src\youtube_bot.py

echo.
echo ✅ 所有必要文件检查通过
echo [%timestamp%] 文件检查通过 >> "%logfile%"

REM 检查依赖包
echo.
echo 检查 Python 依赖包...
python -c "import requests, asyncio" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要的 Python 包
    echo 正在尝试安装依赖包...
    pip install requests asyncio >nul 2>&1
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo [%timestamp%] 错误: 依赖包安装失败 >> "%logfile%"
        goto :error_exit
    )
)
echo ✅ Python 依赖包正常

REM 检查 ixBrowser API
echo.
echo 检查 ixBrowser API 连接...
python -c "import requests; r=requests.post('http://127.0.0.1:53200/api/v2/profile-list', json={'page':1,'limit':1}, timeout=5); exit(0 if r.status_code==200 else 1)" >nul 2>&1
if errorlevel 1 (
    echo ❌ ixBrowser API 服务不可用
    echo 请确保 ixBrowser 软件正在运行
    echo [%timestamp%] 错误: ixBrowser API 不可用 >> "%logfile%"
    goto :error_exit
)
echo ✅ ixBrowser API 连接正常
echo [%timestamp%] ixBrowser API 连接检查通过 >> "%logfile%"

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██                  🚀 开始自动化任务                     ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo [%timestamp%] 开始执行自动化任务 >> "%logfile%"

REM 运行自动化脚本
python sequential_automation.py

REM 检查执行结果
set "exit_code=%errorlevel%"
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "end_time=%dt:~0,4%-%dt:~4,2%-%dt:~6,2% %dt:~8,2%:%dt:~10,2%:%dt:~12,2%"

if %exit_code% equ 0 (
    echo.
    echo ████████████████████████████████████████████████████████████
    echo ██                                                        ██
    echo ██                  ✅ 任务执行完成                       ██
    echo ██                                                        ██
    echo ██        结束时间: %end_time%                    ██
    echo ██                                                        ██
    echo ████████████████████████████████████████████████████████████
    echo.
    echo [%end_time%] 自动化任务执行完成 >> "%logfile%"
) else (
    echo.
    echo ████████████████████████████████████████████████████████████
    echo ██                                                        ██
    echo ██                  ❌ 任务执行失败                       ██
    echo ██                                                        ██
    echo ██        结束时间: %end_time%                    ██
    echo ██        错误代码: %exit_code%                                      ██
    echo ██                                                        ██
    echo ████████████████████████████████████████████████████████████
    echo.
    echo [%end_time%] 自动化任务执行失败，错误代码: %exit_code% >> "%logfile%"
)

echo 📝 详细日志已保存到: %logfile%
echo.

REM 询问是否查看日志
set /p "view_log=是否查看详细日志? (y/N): "
if /i "%view_log%"=="y" (
    echo.
    echo 正在打开日志文件...
    notepad "%logfile%"
)

echo.
echo 按任意键退出...
pause >nul
goto :eof

:error_exit
echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██                  ❌ 环境检查失败                       ██
echo ██                                                        ██
echo ██              请检查错误信息并重试                      ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 📝 错误信息已记录到: %logfile%
echo.
echo 按任意键退出...
pause >nul
exit /b 1
